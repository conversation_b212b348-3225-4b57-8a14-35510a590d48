/**
 * S3 Upload Unit Tests
 * 
 * Tests for lib/s3-upload.ts
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  uploadToS3,
  deleteFromS3,
  fileExistsInS3,
  generateS3Key,
  extractS3KeyFromUrl,
  S3UploadError,
} from '@/lib/s3-upload'

// Mock AWS SDK
vi.mock('@aws-sdk/lib-storage', () => ({
  Upload: vi.fn().mockImplementation(() => ({
    done: vi.fn().mockResolvedValue({
      Key: 'test-key',
      Location: 'https://test-bucket.s3.amazonaws.com/test-key',
    }),
  })),
}))

vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn().mockImplementation(() => ({
    send: vi.fn(),
  })),
  DeleteObjectCommand: vi.fn(),
  HeadObjectCommand: vi.fn(),
}))

vi.mock('@/lib/s3-client', () => ({
  s3Client: {
    send: vi.fn(),
  },
  S3_CONFIG: {
    REGION: 'ap-southeast-1',
    BUCKET_NAME: 'test-bucket',
    ACCESS_KEY_ID: 'test-key',
    SECRET_ACCESS_KEY: 'test-secret',
    PUBLIC_URL: 'https://test-bucket.s3.ap-southeast-1.amazonaws.com',
  },
  validateS3Config: vi.fn(),
}))

describe('S3 Upload Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('uploadToS3', () => {
    it('should upload file successfully', async () => {
      const buffer = Buffer.from('test file content')
      const key = 'test-key'
      const contentType = 'image/jpeg'

      const result = await uploadToS3(buffer, key, contentType)

      expect(result).toEqual({
        url: 'https://test-bucket.s3.ap-southeast-1.amazonaws.com/test-key',
        key: 'test-key',
        size: buffer.length,
        contentType: 'image/jpeg',
      })
    })

    it('should throw error for empty buffer', async () => {
      const buffer = Buffer.alloc(0)
      const key = 'test-key'
      const contentType = 'image/jpeg'

      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow(S3UploadError)
      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow('File buffer is empty')
    })

    it('should throw error for empty key', async () => {
      const buffer = Buffer.from('test')
      const key = ''
      const contentType = 'image/jpeg'

      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow(S3UploadError)
      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow('S3 key is required')
    })

    it('should throw error for empty content type', async () => {
      const buffer = Buffer.from('test')
      const key = 'test-key'
      const contentType = ''

      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow(S3UploadError)
      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow('Content type is required')
    })

    it('should handle upload failure', async () => {
      const { Upload } = await import('@aws-sdk/lib-storage')
      const mockUpload = Upload as any
      mockUpload.mockImplementationOnce(() => ({
        done: vi.fn().mockRejectedValue(new Error('Upload failed')),
      }))

      const buffer = Buffer.from('test')
      const key = 'test-key'
      const contentType = 'image/jpeg'

      await expect(uploadToS3(buffer, key, contentType)).rejects.toThrow(S3UploadError)
    })
  })

  describe('deleteFromS3', () => {
    it('should delete file successfully', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      const mockSend = s3Client.send as any
      mockSend.mockResolvedValueOnce({})

      await expect(deleteFromS3('test-key')).resolves.not.toThrow()
      expect(mockSend).toHaveBeenCalledTimes(1)
    })

    it('should throw error for empty key', async () => {
      await expect(deleteFromS3('')).rejects.toThrow(S3UploadError)
      await expect(deleteFromS3('')).rejects.toThrow('S3 key is required for deletion')
    })

    it('should handle deletion failure', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      const mockSend = s3Client.send as any
      mockSend.mockRejectedValueOnce(new Error('Delete failed'))

      await expect(deleteFromS3('test-key')).rejects.toThrow(S3UploadError)
    })
  })

  describe('fileExistsInS3', () => {
    it('should return true when file exists', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      const mockSend = s3Client.send as any
      mockSend.mockResolvedValueOnce({})

      const result = await fileExistsInS3('test-key')
      expect(result).toBe(true)
    })

    it('should return false when file does not exist', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      const mockSend = s3Client.send as any
      const notFoundError = new Error('Not Found')
      ;(notFoundError as any).name = 'NotFound'
      mockSend.mockRejectedValueOnce(notFoundError)

      const result = await fileExistsInS3('test-key')
      expect(result).toBe(false)
    })

    it('should return false for empty key', async () => {
      const result = await fileExistsInS3('')
      expect(result).toBe(false)
    })

    it('should return false for other errors', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      const mockSend = s3Client.send as any
      mockSend.mockRejectedValueOnce(new Error('Network error'))

      const result = await fileExistsInS3('test-key')
      expect(result).toBe(false)
    })
  })

  describe('generateS3Key', () => {
    it('should generate key for logo', () => {
      const key = generateS3Key('user123', 'logo', 'jpg')
      expect(key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
    })

    it('should generate key for product-image', () => {
      const key = generateS3Key('user456', 'product-image', 'png')
      expect(key).toMatch(/^projects\/user456\/product-image\/\d+-[a-z0-9]+\.png$/)
    })

    it('should work without extension', () => {
      const key = generateS3Key('user123', 'logo')
      expect(key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+$/)
    })

    it('should throw error for empty user ID', () => {
      expect(() => generateS3Key('', 'logo')).toThrow(S3UploadError)
      expect(() => generateS3Key('', 'logo')).toThrow('User ID is required')
    })

    it('should handle extension with dot', () => {
      const key = generateS3Key('user123', 'logo', '.jpg')
      expect(key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
    })
  })

  describe('extractS3KeyFromUrl', () => {
    const baseUrl = 'https://test-bucket.s3.ap-southeast-1.amazonaws.com'

    it('should extract key from valid S3 URL', () => {
      const url = `${baseUrl}/projects/user123/logo/123-abc.jpg`
      const key = extractS3KeyFromUrl(url)
      expect(key).toBe('projects/user123/logo/123-abc.jpg')
    })

    it('should handle URL with query parameters', () => {
      const url = `${baseUrl}/projects/user123/logo/123-abc.jpg?version=1&cache=false`
      const key = extractS3KeyFromUrl(url)
      expect(key).toBe('projects/user123/logo/123-abc.jpg')
    })

    it('should return null for invalid URL', () => {
      const url = 'https://other-domain.com/file.jpg'
      const key = extractS3KeyFromUrl(url)
      expect(key).toBeNull()
    })

    it('should return null for empty URL', () => {
      const key = extractS3KeyFromUrl('')
      expect(key).toBeNull()
    })

    it('should return null for null URL', () => {
      const key = extractS3KeyFromUrl(null as any)
      expect(key).toBeNull()
    })

    it('should handle malformed URLs gracefully', () => {
      const key = extractS3KeyFromUrl('not-a-url')
      expect(key).toBeNull()
    })
  })

  describe('S3UploadError', () => {
    it('should create error with message', () => {
      const error = new S3UploadError('Test error')
      expect(error.message).toBe('Test error')
      expect(error.name).toBe('S3UploadError')
      expect(error).toBeInstanceOf(Error)
    })

    it('should create error with cause', () => {
      const cause = new Error('Original error')
      const error = new S3UploadError('Test error', cause)
      expect(error.message).toBe('Test error')
      expect(error.cause).toBe(cause)
    })
  })
})
