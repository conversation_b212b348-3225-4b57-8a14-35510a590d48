#!/usr/bin/env tsx

/**
 * S3 Configuration Validation Script
 * 
 * Validates AWS S3 configuration and tests connectivity
 * Usage: bun run s3:validate
 */

import { config } from 'dotenv'
import { validateS3Config, s3Client, S3_CONFIG } from '../lib/s3-client'
import { HeadBucketCommand } from '@aws-sdk/client-s3'

// Load environment variables
config()

async function validateS3Configuration() {
  console.log('🔍 Validating S3 Configuration...\n')

  try {
    // 1. Validate environment variables
    console.log('1. Checking environment variables...')
    validateS3Config()
    console.log('✅ All required environment variables are set\n')

    // 2. Display configuration
    console.log('2. Current S3 Configuration:')
    console.log(`   Region: ${S3_CONFIG.REGION}`)
    console.log(`   Bucket: ${S3_CONFIG.BUCKET_NAME}`)
    console.log(`   Public URL: ${S3_CONFIG.PUBLIC_URL}`)
    console.log(`   Access Key ID: ${process.env.AWS_S3_ACCESS_KEY_ID?.substring(0, 8)}...`)
    console.log('')

    // 3. Test S3 connectivity
    console.log('3. Testing S3 connectivity...')
    const headBucketCommand = new HeadBucketCommand({
      Bucket: S3_CONFIG.BUCKET_NAME
    })

    await s3Client.send(headBucketCommand)
    console.log('✅ Successfully connected to S3 bucket\n')

    // 4. Test permissions (optional)
    console.log('4. S3 Configuration Summary:')
    console.log('✅ Environment variables: Valid')
    console.log('✅ S3 connectivity: Working')
    console.log('✅ Bucket access: Confirmed')
    console.log('\n🎉 S3 configuration is ready for use!')

  } catch (error) {
    console.error('❌ S3 Configuration Error:')
    
    if (error instanceof Error) {
      if (error.message.includes('Missing required S3 environment variables')) {
        console.error('   Missing environment variables. Please check your .env file.')
        console.error('   Required variables:')
        console.error('   - AWS_S3_REGION')
        console.error('   - AWS_S3_BUCKET_NAME')
        console.error('   - AWS_S3_ACCESS_KEY_ID')
        console.error('   - AWS_S3_SECRET_ACCESS_KEY')
        console.error('   - AWS_S3_PUBLIC_URL')
      } else if (error.message.includes('NoSuchBucket')) {
        console.error('   S3 bucket does not exist or is not accessible.')
        console.error(`   Bucket: ${S3_CONFIG.BUCKET_NAME}`)
        console.error(`   Region: ${S3_CONFIG.REGION}`)
      } else if (error.message.includes('InvalidAccessKeyId')) {
        console.error('   Invalid AWS access key ID.')
        console.error('   Please check your AWS credentials.')
      } else if (error.message.includes('SignatureDoesNotMatch')) {
        console.error('   Invalid AWS secret access key.')
        console.error('   Please check your AWS credentials.')
      } else if (error.message.includes('AccessDenied')) {
        console.error('   Access denied to S3 bucket.')
        console.error('   Please check your IAM permissions.')
      } else {
        console.error(`   ${error.message}`)
      }
    } else {
      console.error('   Unknown error occurred')
    }

    console.error('\n💡 Troubleshooting tips:')
    console.error('   1. Verify your .env file has all required AWS S3 variables')
    console.error('   2. Check that your AWS credentials are correct')
    console.error('   3. Ensure the S3 bucket exists and is in the correct region')
    console.error('   4. Verify IAM permissions for the bucket')
    
    process.exit(1)
  }
}

// Run validation
if (require.main === module) {
  validateS3Configuration().catch(console.error)
}
