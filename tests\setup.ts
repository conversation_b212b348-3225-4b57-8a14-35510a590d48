/**
 * Vitest Test Setup
 * 
 * Global test configuration and mocks
 */

import { vi } from 'vitest'
import { config } from 'dotenv'
import '@testing-library/jest-dom'

// Load environment variables for testing
config({ path: '.env' })

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Mock fetch for testing
global.fetch = vi.fn()

// Mock File constructor for Node.js environment
global.File = class File {
  name: string
  size: number
  type: string
  lastModified: number
  
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    this.name = name
    this.type = options?.type || ''
    this.lastModified = options?.lastModified || Date.now()
    
    // Calculate size from bits
    this.size = bits.reduce((total, bit) => {
      if (bit instanceof ArrayBuffer) {
        return total + bit.byteLength
      } else if (bit instanceof Uint8Array) {
        return total + bit.length
      } else if (typeof bit === 'string') {
        return total + new TextEncoder().encode(bit).length
      }
      return total
    }, 0)
  }
  
  arrayBuffer(): Promise<ArrayBuffer> {
    return Promise.resolve(new ArrayBuffer(this.size))
  }
  
  text(): Promise<string> {
    return Promise.resolve('')
  }
  
  stream(): ReadableStream {
    return new ReadableStream()
  }
  
  slice(): Blob {
    return new Blob()
  }
} as any

// Setup test environment variables if not present
if (!process.env.AWS_S3_REGION) {
  process.env.AWS_S3_REGION = 'ap-southeast-1'
}
if (!process.env.AWS_S3_BUCKET_NAME) {
  process.env.AWS_S3_BUCKET_NAME = 'test-bucket'
}
if (!process.env.AWS_S3_ACCESS_KEY_ID) {
  process.env.AWS_S3_ACCESS_KEY_ID = 'test-access-key'
}
if (!process.env.AWS_S3_SECRET_ACCESS_KEY) {
  process.env.AWS_S3_SECRET_ACCESS_KEY = 'test-secret-key'
}
if (!process.env.AWS_S3_PUBLIC_URL) {
  process.env.AWS_S3_PUBLIC_URL = 'https://test-bucket.s3.ap-southeast-1.amazonaws.com'
}
