/**
 * File Validation Utilities for S3 Upload
 * 
 * <PERSON>ung cấp các functions để validate files trước khi upload lên S3
 * Tương thích với UploadThing constraints hiện tại
 */

/**
 * Allowed Image MIME Types
 */
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
] as const

/**
 * File Size Limits (giữ nguyên như UploadThing)
 */
export const FILE_SIZE_LIMITS = {
  LOGO: 1024 * 1024, // 1MB
  PRODUCT_IMAGE: 1024 * 1024, // 1MB
  MAX_FILE_SIZE: 1024 * 1024, // 1MB (general limit)
} as const

/**
 * File Validation Error Types
 */
export type FileValidationErrorCode = 
  | 'FILE_TOO_LARGE' 
  | 'INVALID_FILE_TYPE' 
  | 'NO_FILE'
  | 'INVALID_FILE_NAME'
  | 'FILE_CORRUPTED'

export interface FileValidationError {
  code: FileValidationErrorCode
  message: string
  details?: string
}

/**
 * File Type Definitions
 */
export type UploadFileType = 'logo' | 'product-image'

/**
 * Validate image file for upload
 * 
 * @param file - File object to validate
 * @param type - Type of upload ('logo' | 'product-image')
 * @returns FileValidationError | null
 */
export function validateImageFile(
  file: File, 
  type: UploadFileType = 'logo'
): FileValidationError | null {
  // Check if file exists
  if (!file) {
    return {
      code: 'NO_FILE',
      message: 'No file provided'
    }
  }

  // Check file size
  const maxSize = type === 'logo' ? FILE_SIZE_LIMITS.LOGO : FILE_SIZE_LIMITS.PRODUCT_IMAGE
  if (file.size > maxSize) {
    return {
      code: 'FILE_TOO_LARGE',
      message: `File size must be less than ${formatFileSize(maxSize)}`,
      details: `Current size: ${formatFileSize(file.size)}, Max allowed: ${formatFileSize(maxSize)}`
    }
  }

  // Check file type
  if (!ALLOWED_IMAGE_TYPES.includes(file.type as typeof ALLOWED_IMAGE_TYPES[number])) {
    return {
      code: 'INVALID_FILE_TYPE',
      message: 'Only image files (JPEG, PNG, WebP, GIF) are allowed',
      details: `Received: ${file.type}, Allowed: ${ALLOWED_IMAGE_TYPES.join(', ')}`
    }
  }

  // Check file name
  const nameValidation = validateFileName(file.name)
  if (nameValidation) {
    return nameValidation
  }

  // Basic file corruption check
  if (file.size === 0) {
    return {
      code: 'FILE_CORRUPTED',
      message: 'File appears to be corrupted (0 bytes)',
    }
  }

  return null
}

/**
 * Validate file name
 * 
 * @param fileName - Name of the file
 * @returns FileValidationError | null
 */
export function validateFileName(fileName: string): FileValidationError | null {
  if (!fileName || fileName.trim() === '') {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name is required'
    }
  }

  // Check for dangerous characters
  const dangerousChars = /[<>:"/\\|?*\x00-\x1f]/
  if (dangerousChars.test(fileName)) {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name contains invalid characters',
      details: 'File name cannot contain: < > : " / \\ | ? * or control characters'
    }
  }

  // Check file name length
  if (fileName.length > 255) {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name is too long (max 255 characters)',
      details: `Current length: ${fileName.length}`
    }
  }

  return null
}

/**
 * Get file extension from file name or MIME type
 * 
 * @param file - File object
 * @returns string - File extension without dot
 */
export function getFileExtension(file: File): string {
  // Try to get extension from file name first
  const nameExtension = file.name.split('.').pop()?.toLowerCase()
  if (nameExtension && isValidImageExtension(nameExtension)) {
    return nameExtension
  }

  // Fallback to MIME type
  switch (file.type) {
    case 'image/jpeg':
    case 'image/jpg':
      return 'jpg'
    case 'image/png':
      return 'png'
    case 'image/webp':
      return 'webp'
    case 'image/gif':
      return 'gif'
    default:
      return 'jpg' // Default fallback
  }
}

/**
 * Check if extension is valid for images
 * 
 * @param extension - File extension
 * @returns boolean
 */
export function isValidImageExtension(extension: string): boolean {
  const validExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif']
  return validExtensions.includes(extension.toLowerCase())
}

/**
 * Format file size for human reading
 * 
 * @param bytes - File size in bytes
 * @returns string - Formatted file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Generate file key for S3 upload
 * Tương thích với cấu trúc UploadThing hiện tại
 * 
 * @param userId - User ID
 * @param type - Upload type
 * @param file - File object (optional, for extension)
 * @returns string - S3 key
 */
export function generateFileKey(
  userId: string, 
  type: UploadFileType,
  file?: File
): string {
  if (!userId || userId.trim() === '') {
    throw new Error('User ID is required for generating file key')
  }

  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  
  // Get file extension if file is provided
  const extension = file ? getFileExtension(file) : 'jpg'
  
  return `projects/${userId}/${type}/${timestamp}-${random}.${extension}`
}

/**
 * Validate multiple files at once
 * 
 * @param files - Array of files to validate
 * @param type - Upload type
 * @returns Array of validation results
 */
export function validateMultipleFiles(
  files: File[], 
  type: UploadFileType
): Array<{ file: File; error: FileValidationError | null }> {
  return files.map(file => ({
    file,
    error: validateImageFile(file, type)
  }))
}

/**
 * Check if file is an image based on MIME type
 * 
 * @param file - File object
 * @returns boolean
 */
export function isImageFile(file: File): boolean {
  return ALLOWED_IMAGE_TYPES.includes(file.type as typeof ALLOWED_IMAGE_TYPES[number])
}

/**
 * Get recommended image dimensions for different types
 * 
 * @param type - Upload type
 * @returns Object with width and height recommendations
 */
export function getRecommendedDimensions(type: UploadFileType) {
  switch (type) {
    case 'logo':
      return {
        width: 256,
        height: 256,
        aspectRatio: '1:1',
        description: 'Square logo for best display'
      }
    case 'product-image':
      return {
        width: 800,
        height: 450,
        aspectRatio: '16:9',
        description: 'Landscape image for product showcase'
      }
    default:
      return {
        width: 800,
        height: 600,
        aspectRatio: '4:3',
        description: 'Standard image dimensions'
      }
  }
}
