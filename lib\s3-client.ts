import { S3Client } from "@aws-sdk/client-s3"

/**
 * AWS S3 Client Configuration
 * 
 * Tạo và cấu hình S3 client để sử dụng cho upload files
 * Sử dụng environment variables để cấu hình credentials và region
 */
export const s3Client = new S3Client({
  region: process.env.AWS_S3_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY!,
  },
  maxAttempts: 3,
  retryMode: "standard",
  // Cấu hình timeout và retry
  requestHandler: {
    requestTimeout: 30000, // 30 seconds
    httpsAgent: {
      maxSockets: 25,
    },
  },
})

/**
 * S3 Configuration Constants
 */
export const S3_CONFIG = {
  BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME!,
  PUBLIC_URL: process.env.AWS_S3_PUBLIC_URL!,
  REGION: process.env.AWS_S3_REGION!,
} as const

/**
 * Validate S3 Configuration
 * Kiểm tra xem tất cả environment variables cần thiết đã được cấu hình chưa
 */
export function validateS3Config() {
  const requiredEnvVars = [
    'AWS_S3_REGION',
    'AWS_S3_BUCKET_NAME', 
    'AWS_S3_ACCESS_KEY_ID',
    'AWS_S3_SECRET_ACCESS_KEY',
    'AWS_S3_PUBLIC_URL'
  ]

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required S3 environment variables: ${missingVars.join(', ')}`
    )
  }
}

// Validate configuration on import
if (process.env.NODE_ENV !== 'test') {
  validateS3Config()
}
