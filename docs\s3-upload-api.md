# 📚 S3 Upload API Documentation

## 🌐 API Endpoints

### **POST /api/upload/logo**

Upload logo images to S3.

#### **Request**
```http
POST /api/upload/logo
Content-Type: multipart/form-data

file: [File] - Image file to upload
```

#### **Response**
```json
{
  "success": true,
  "data": {
    "url": "https://bucket.s3.region.amazonaws.com/projects/user123/logo/123-abc.jpg",
    "key": "projects/user123/logo/123-abc.jpg",
    "size": 50000,
    "contentType": "image/jpeg",
    "uploadedBy": "user123",
    "fileUrl": "https://bucket.s3.region.amazonaws.com/projects/user123/logo/123-abc.jpg"
  }
}
```

#### **Error Response**
```json
{
  "success": false,
  "error": "FILE_TOO_LARGE",
  "message": "File size must be less than 1MB",
  "details": "Current size: 2MB, Max allowed: 1MB"
}
```

### **POST /api/upload/product-image**

Upload product images to S3.

#### **Request**
```http
POST /api/upload/product-image
Content-Type: multipart/form-data

file: [File] - Image file to upload
```

#### **Response**
Same format as logo upload.

## 🔧 Core Functions

### **uploadToS3()**

```typescript
async function uploadToS3(
  file: Buffer,
  key: string,
  contentType: string
): Promise<UploadResult>
```

#### **Parameters**
- `file: Buffer` - File buffer to upload
- `key: string` - S3 key (path) for the file
- `contentType: string` - MIME type of the file

#### **Returns**
```typescript
interface UploadResult {
  url: string        // Public URL of uploaded file
  key: string        // S3 key of uploaded file
  size: number       // File size in bytes
  contentType: string // MIME type
}
```

#### **Example**
```typescript
const buffer = Buffer.from(await file.arrayBuffer())
const key = generateS3Key(userId, 'logo', 'jpg')
const result = await uploadToS3(buffer, key, 'image/jpeg')

console.log(result.url) // https://bucket.s3.region.amazonaws.com/...
```

### **deleteFromS3()**

```typescript
async function deleteFromS3(key: string): Promise<void>
```

#### **Parameters**
- `key: string` - S3 key of file to delete

#### **Example**
```typescript
await deleteFromS3('projects/user123/logo/123-abc.jpg')
```

### **fileExistsInS3()**

```typescript
async function fileExistsInS3(key: string): Promise<boolean>
```

#### **Parameters**
- `key: string` - S3 key to check

#### **Returns**
- `boolean` - True if file exists, false otherwise

#### **Example**
```typescript
const exists = await fileExistsInS3('projects/user123/logo/123-abc.jpg')
if (exists) {
  console.log('File exists in S3')
}
```

### **generateS3Key()**

```typescript
function generateS3Key(
  userId: string,
  type: 'logo' | 'product-image',
  extension?: string
): string
```

#### **Parameters**
- `userId: string` - User ID
- `type: 'logo' | 'product-image'` - File type
- `extension?: string` - File extension (optional)

#### **Returns**
- `string` - Generated S3 key

#### **Example**
```typescript
const key = generateS3Key('user123', 'logo', 'jpg')
// Returns: "projects/user123/logo/1640995200000-abc123.jpg"
```

## 🔍 File Validation

### **validateImageFile()**

```typescript
function validateImageFile(
  file: File,
  type: 'logo' | 'product-image'
): FileValidationError | null
```

#### **Parameters**
- `file: File` - File to validate
- `type: 'logo' | 'product-image'` - Upload type

#### **Returns**
- `FileValidationError | null` - Error object or null if valid

#### **Validation Rules**
- **File size**: Max 1MB
- **File types**: JPEG, PNG, WebP, GIF
- **File name**: Valid characters only
- **File integrity**: Not corrupted

#### **Error Codes**
- `FILE_TOO_LARGE` - File exceeds size limit
- `INVALID_FILE_TYPE` - Unsupported file type
- `NO_FILE` - No file provided
- `INVALID_FILE_NAME` - Invalid file name
- `FILE_CORRUPTED` - File appears corrupted

#### **Example**
```typescript
const error = validateImageFile(file, 'logo')
if (error) {
  console.error(`Validation failed: ${error.message}`)
  // Handle error
} else {
  // File is valid, proceed with upload
}
```

## ⚛️ React Components

### **S3UploadButton**

```typescript
interface S3UploadButtonProps {
  endpoint: 'logo' | 'product-image' | 'projectLogo' | 'projectProductImage'
  onUploadBegin?: () => void
  onClientUploadComplete?: (res: S3UploadResponse[]) => void
  onUploadError?: (error: Error) => void
  disabled?: boolean
  className?: string
  appearance?: {
    button?: string
    allowedContent?: string
  }
  content?: {
    button?: (props: { ready: boolean; isUploading: boolean }) => React.ReactNode
  }
}
```

#### **Props**
- `endpoint` - Upload endpoint type
- `onUploadBegin` - Called when upload starts
- `onClientUploadComplete` - Called when upload completes
- `onUploadError` - Called when upload fails
- `disabled` - Disable the button
- `className` - Additional CSS classes
- `appearance` - Custom styling
- `content` - Custom button content

#### **Example**
```tsx
<S3UploadButton
  endpoint="logo"
  onUploadBegin={() => setUploading(true)}
  onClientUploadComplete={(res) => {
    setUploading(false)
    setImageUrl(res[0].data.url)
  }}
  onUploadError={(error) => {
    setUploading(false)
    setError(error.message)
  }}
  appearance={{
    button: "custom-button-class"
  }}
  content={{
    button: ({ ready, isUploading }) => (
      isUploading ? "Uploading..." : "Upload Image"
    )
  }}
/>
```

## 🔄 Compatibility Layer

### **UploadButton (Compatibility)**

The existing `UploadButton` from `@/lib/uploadthing` continues to work unchanged:

```tsx
import { UploadButton } from "@/lib/uploadthing"

<UploadButton
  endpoint="projectLogo"
  onClientUploadComplete={(res) => {
    // res[0].serverData.fileUrl contains the uploaded file URL
    setImageUrl(res[0].serverData.fileUrl)
  }}
  onUploadError={(error) => {
    console.error("Upload error:", error)
  }}
/>
```

#### **Endpoint Mapping**
- `projectLogo` → `/api/upload/logo`
- `projectProductImage` → `/api/upload/product-image`

## 🛡️ Error Handling

### **S3UploadError**

Custom error class for S3 operations:

```typescript
class S3UploadError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message)
    this.name = 'S3UploadError'
  }
}
```

### **Common Error Scenarios**

#### **1. Authentication Errors**
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "You must be logged in to upload files"
}
```

#### **2. Validation Errors**
```json
{
  "success": false,
  "error": "FILE_TOO_LARGE",
  "message": "File size must be less than 1MB",
  "details": "Current size: 2MB, Max allowed: 1MB"
}
```

#### **3. S3 Errors**
```json
{
  "success": false,
  "error": "Upload failed",
  "message": "Failed to upload file to S3: Access Denied"
}
```

## 📊 Response Formats

### **Success Response**
```typescript
interface SuccessResponse {
  success: true
  data: {
    url: string
    key: string
    size: number
    contentType: string
    uploadedBy: string
    fileUrl: string // UploadThing compatibility
  }
}
```

### **Error Response**
```typescript
interface ErrorResponse {
  success: false
  error: string
  message: string
  details?: string
}
```

### **UploadThing Compatible Response**
```typescript
// Array format for compatibility
[{
  serverData: {
    fileUrl: string
    uploadedBy: string
  },
  url: string
  key: string
  size: number
  type: string
}]
```

## 🔧 Configuration

### **S3 Client Configuration**
```typescript
export const S3_CONFIG = {
  REGION: process.env.AWS_S3_REGION!,
  BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME!,
  ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID!,
  SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY!,
  PUBLIC_URL: process.env.AWS_S3_PUBLIC_URL!,
}
```

### **File Size Limits**
```typescript
export const FILE_SIZE_LIMITS = {
  LOGO: 1024 * 1024, // 1MB
  PRODUCT_IMAGE: 1024 * 1024, // 1MB
  MAX_FILE_SIZE: 1024 * 1024, // 1MB
}
```

### **Allowed File Types**
```typescript
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif'
]
```

---

**API Documentation v1.0** | *Last updated: Migration completion*
